// functions/src/index.ts
import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import * as cors from "cors";

// Initialize Firebase Admin
admin.initializeApp();

const corsHandler = cors({origin: true});

// Types
interface CustomClaims {
  role: "basic" | "premium" | "enterprise" | "admin";
  apps: {[appId: string]: {access: boolean; level?: string}};
  subscription?: {
    plan: "free" | "basic" | "premium" | "enterprise";
    status: "active" | "canceled" | "expired" | "trial";
    expiresAt?: string;
  };
  isAdmin?: boolean;
}

// Function to update user custom claims
export const updateUserClaims = functions.https.onCall(async (data, context) => {
  // Check if the caller is authenticated and is an admin
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "Must be authenticated");
  }

  // Get caller's claims to check if they're admin
  const callerClaims = context.auth.token;
  if (!callerClaims.isAdmin && callerClaims.role !== "admin") {
    throw new functions.https.HttpsError("permission-denied", "Must be admin");
  }

  const {uid, claims} = data;

  if (!uid || !claims) {
    throw new functions.https.HttpsError("invalid-argument", "UID and claims are required");
  }

  try {
    // Update the user's custom claims
    await admin.auth().setCustomUserClaims(uid, claims);

    // Log the change
    console.log(`Updated claims for user ${uid}:`, claims);

    return {success: true, message: "Claims updated successfully"};
  } catch (error) {
    console.error("Error updating claims:", error);
    throw new functions.https.HttpsError("internal", "Failed to update claims");
  }
});

// Function to get user with claims
export const getUserWithClaims = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "Must be authenticated");
  }

  const callerClaims = context.auth.token;
  if (!callerClaims.isAdmin && callerClaims.role !== "admin") {
    throw new functions.https.HttpsError("permission-denied", "Must be admin");
  }

  const {uid} = data;

  if (!uid) {
    throw new functions.https.HttpsError("invalid-argument", "UID is required");
  }

  try {
    const userRecord = await admin.auth().getUser(uid);
    return {
      uid: userRecord.uid,
      email: userRecord.email,
      displayName: userRecord.displayName,
      customClaims: userRecord.customClaims || {},
      createdAt: userRecord.metadata.creationTime,
      lastLoginAt: userRecord.metadata.lastSignInTime,
    };
  } catch (error) {
    console.error("Error getting user:", error);
    throw new functions.https.HttpsError("internal", "Failed to get user");
  }
});

// Function to list all users (with pagination)
export const listUsers = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "Must be authenticated");
  }

  const callerClaims = context.auth.token;
  if (!callerClaims.isAdmin && callerClaims.role !== "admin") {
    throw new functions.https.HttpsError("permission-denied", "Must be admin");
  }

  const {maxResults = 100, pageToken} = data;

  try {
    const listUsersResult = await admin.auth().listUsers(maxResults, pageToken);

    const users = listUsersResult.users.map((user: admin.auth.UserRecord) => ({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      customClaims: user.customClaims || {},
      createdAt: user.metadata.creationTime,
      lastLoginAt: user.metadata.lastSignInTime,
      disabled: user.disabled,
    }));

    return {
      users,
      pageToken: listUsersResult.pageToken,
    };
  } catch (error) {
    console.error("Error listing users:", error);
    throw new functions.https.HttpsError("internal", "Failed to list users");
  }
});

// Function to initialize admin user (run once)
export const initializeAdmin = functions.https.onRequest((req, res) => {
  return corsHandler(req, res, async () => {
    try {
      const {email, password, adminSecret} = req.body;

      // Simple security check - you should use a proper secret
      if (adminSecret !== "your-secret-key-change-this") {
        res.status(403).json({error: "Invalid admin secret"});
        return;
      }

      if (!email || !password) {
        res.status(400).json({error: "Email and password required"});
        return;
      }

      // Check if user already exists
      try {
        const existingUser = await admin.auth().getUserByEmail(email);
        res.status(409).json({error: "User already exists", uid: existingUser.uid});
        return;
      } catch (error: any) {
        // User doesn't exist, continue with creation
        if (error.code !== "auth/user-not-found") {
          throw error;
        }
      }

      // Create user
      const userRecord = await admin.auth().createUser({
        email,
        password,
        emailVerified: true,
      });

      // Set admin claims
      const adminClaims: CustomClaims = {
        role: "admin",
        isAdmin: true,
        apps: {
          "app-dashboard": {access: true, level: "full"},
          "app-reports": {access: true, level: "full"},
          "app-billing": {access: true, level: "full"},
          "app-analytics": {access: true, level: "full"},
          "app-api": {access: true, level: "full"},
        },
        subscription: {
          plan: "enterprise",
          status: "active",
        },
      };

      await admin.auth().setCustomUserClaims(userRecord.uid, adminClaims);

      console.log(`Admin user created: ${email} (${userRecord.uid})`);

      res.json({
        success: true,
        uid: userRecord.uid,
        message: "Admin user created successfully",
      });
    } catch (error) {
      console.error("Error creating admin:", error);
      res.status(500).json({
        error: "Failed to create admin user",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  });
});

// Function to promote existing user to admin
export const promoteToAdmin = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "Must be authenticated");
  }

  const callerClaims = context.auth.token;
  if (!callerClaims.isAdmin && callerClaims.role !== "admin") {
    throw new functions.https.HttpsError("permission-denied", "Must be admin");
  }

  const {uid} = data;

  if (!uid) {
    throw new functions.https.HttpsError("invalid-argument", "UID is required");
  }

  try {
    const adminClaims: CustomClaims = {
      role: "admin",
      isAdmin: true,
      apps: {
        "app-dashboard": {access: true, level: "full"},
        "app-reports": {access: true, level: "full"},
        "app-billing": {access: true, level: "full"},
        "app-analytics": {access: true, level: "full"},
        "app-api": {access: true, level: "full"},
      },
      subscription: {
        plan: "enterprise",
        status: "active",
      },
    };

    await admin.auth().setCustomUserClaims(uid, adminClaims);

    return {success: true, message: "User promoted to admin"};
  } catch (error) {
    console.error("Error promoting user:", error);
    throw new functions.https.HttpsError("internal", "Failed to promote user");
  }
});

// Function to set default permissions for new users
export const setDefaultUserPermissions = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "Must be authenticated");
  }

  const {uid, role = "basic"} = data;

  if (!uid) {
    throw new functions.https.HttpsError("invalid-argument", "UID is required");
  }

  try {
    let plan: "free" | "basic" | "premium" | "enterprise";
    switch (role) {
      case "premium":
        plan = "premium";
        break;
      case "enterprise":
        plan = "enterprise";
        break;
      case "admin":
        plan = "enterprise";
        break;
      default:
        plan = "basic";
    }

    const defaultClaims: CustomClaims = {
      role,
      apps: {
        "app-dashboard": {access: true, level: "read"},
      },
      subscription: {
        plan,
        status: "active",
      },
    };

    await admin.auth().setCustomUserClaims(uid, defaultClaims);

    return {success: true, message: "Default permissions set"};
  } catch (error) {
    console.error("Error setting default permissions:", error);
    throw new functions.https.HttpsError("internal", "Failed to set permissions");
  }
});

// Function to create a regular user (for testing)
export const createUser = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "Must be authenticated");
  }

  const callerClaims = context.auth.token;
  if (!callerClaims.isAdmin && callerClaims.role !== "admin") {
    throw new functions.https.HttpsError("permission-denied", "Must be admin");
  }

  const {email, password, role = "basic"} = data;

  if (!email || !password) {
    throw new functions.https.HttpsError("invalid-argument", "Email and password are required");
  }

  try {
    // Create user
    const userRecord = await admin.auth().createUser({
      email,
      password,
      emailVerified: true,
    });

    // Set default permissions
    const defaultPermissionsData = {uid: userRecord.uid, role};
    await setDefaultUserPermissions(defaultPermissionsData, context);

    return {
      success: true,
      uid: userRecord.uid,
      email: userRecord.email,
      message: "User created successfully",
    };
  } catch (error) {
    console.error("Error creating user:", error);
    throw new functions.https.HttpsError("internal", "Failed to create user");
  }
});

// TEMPORARY FUNCTION - Setup admin for initial configuration (DELETE AFTER SETUP)
export const setupAdmin = functions.https.onRequest((req, res) => {
  return corsHandler(req, res, async () => {
    try {
      const {uid, setupSecret} = req.body;

      if (setupSecret !== "setup-cerebro-admin-2025") {
        res.status(403).json({error: "Invalid setup secret"});
        return;
      }

      if (!uid) {
        res.status(400).json({error: "UID is required"});
        return;
      }

      const adminClaims: CustomClaims = {
        role: "admin",
        isAdmin: true,
        apps: {
          "app-dashboard": {access: true, level: "full"},
          "app-reports": {access: true, level: "full"},
          "app-billing": {access: true, level: "full"},
          "app-analytics": {access: true, level: "full"},
          "app-api": {access: true, level: "full"},
        },
        subscription: {
          plan: "enterprise",
          status: "active",
        },
      };

      await admin.auth().setCustomUserClaims(uid, adminClaims);

      console.log(`Admin claims set for user ${uid}`);

      res.json({
        success: true,
        message: "Admin claims set successfully",
        claims: adminClaims,
      });
    } catch (error) {
      console.error("Error setting admin claims:", error);
      res.status(500).json({
        error: "Failed to set admin claims",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  });
});
